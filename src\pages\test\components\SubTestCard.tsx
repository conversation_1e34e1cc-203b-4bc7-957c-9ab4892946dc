import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Headphones, Mic, BookOpen, PenTool } from "lucide-react";

interface SubTest {
  id: string;
  title: string;
  type: string;
  createdAt: string;
  updatedAt: string;
}

interface SubTestCardProps {
  subTest: SubTest;
  getTestTypeImage: (type: string) => string;
  formatDate: (dateString: string) => string;
}

const getTypeInfo = (type: string) => {
  switch (type.toLowerCase()) {
    case "listening":
      return {
        label: "<PERSON>lem<PERSON>",
        icon: Headphones,
        color: "text-purple-600",
        bgColor: "bg-purple-50",
        borderColor: "border-purple-200",
      };
    case "speaking":
      return {
        label: "Konuşma",
        icon: Mic,
        color: "text-green-600",
        bgColor: "bg-green-50",
        borderColor: "border-green-200",
      };
    case "reading":
      return {
        label: "Okuma",
        icon: BookOpen,
        color: "text-blue-600",
        bgColor: "bg-blue-50",
        borderColor: "border-blue-200",
      };
    case "writing":
    case "academic":
      return {
        label: "Yazma",
        icon: PenTool,
        color: "text-red-600",
        bgColor: "bg-red-50",
        borderColor: "border-red-200",
      };
    default:
      return {
        label: type,
        icon: BookOpen,
        color: "text-gray-600",
        bgColor: "bg-gray-50",
        borderColor: "border-gray-200",
      };
  }
};

const SubTestCard = ({ subTest }: SubTestCardProps) => {
  const typeInfo = getTypeInfo(subTest.type);

  return (
    <Card
      key={subTest.id}
      className={`overflow-hidden hover:shadow-lg transition-all duration-300 ${typeInfo.borderColor} hover:border-red-200 h-[280px] flex flex-col`}
    >
      {/* Header Section with Icon and Badge */}
      <div className={`${typeInfo.bgColor} p-6`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={`p-3 rounded-full bg-white shadow-sm`}>
              <typeInfo.icon className={`h-8 w-8 ${typeInfo.color}`} />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">
                {typeInfo.label}
              </h3>
            </div>
          </div>
          <Badge variant="secondary" className="bg-white/90 text-gray-700">
            <Clock className="h-3 w-3 mr-1" />
            Test
          </Badge>
        </div>
      </div>

      {/* Content Section */}
      <CardContent className="p-6 flex-1 flex flex-col">
        <div className="flex-1">
          <p className="text-gray-600 text-sm">
            {typeInfo.label} becerinizi test edin ve seviyenizi belirleyin.
          </p>
        </div>

        {/* Button Section - Always at bottom */}
        <div className="mt-6">
          <Button className="w-full bg-red-600 hover:bg-red-700 text-white cursor-pointer">
            Teste Başla
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubTestCard;
