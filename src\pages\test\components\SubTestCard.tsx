import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Headphones, Mic, BookOpen, PenTool } from "lucide-react";

interface SubTest {
  id: string;
  title: string;
  type: string;
  createdAt: string;
  updatedAt: string;
}

interface SubTestCardProps {
  subTest: SubTest;
  getTestTypeImage: (type: string) => string;
  formatDate: (dateString: string) => string;
}

const getTypeInfo = (type: string) => {
  switch (type.toLowerCase()) {
    case "listening":
      return {
        label: "<PERSON><PERSON><PERSON>",
        icon: Headphones,
        color: "text-purple-600",
      };
    case "speaking":
      return {
        label: "Konuş<PERSON>",
        icon: Mic,
        color: "text-green-600",
      };
    case "reading":
      return {
        label: "Okuma",
        icon: BookOpen,
        color: "text-blue-600",
      };
    case "writing":
    case "academic":
      return {
        label: "<PERSON>z<PERSON>",
        icon: PenTool,
        color: "text-red-600",
      };
    default:
      return {
        label: type,
        icon: BookOpen,
        color: "text-gray-600",
      };
  }
};

const SubTestCard = ({ subTest }: SubTestCardProps) => {
  const typeInfo = getTypeInfo(subTest.type);

  return (
    <Card
      key={subTest.id}
      className="overflow-hidden hover:shadow-lg transition-all duration-300 border-gray-200 hover:border-red-200 h-[200px] flex flex-col"
    >
      {/* Content Section */}
      <CardContent className="p-6 flex-1 flex flex-col">
        {/* Header with just the text */}
        <div className="flex items-center justify-center mb-6">
          <h3 className="text-xl font-semibold text-gray-900">
            {typeInfo.label}
          </h3>
        </div>

        {/* Button Section - Always at bottom */}
        <div className="mt-auto">
          <Button className="w-full bg-red-600 hover:bg-red-700 text-white cursor-pointer">
            Teste Başla
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubTestCard;
